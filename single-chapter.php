<?php
get_header();

// الحصول على معلومات الرواية
$post = $wp_query->post;
$novel_id = get_post_meta($post->ID, '_novel_id', true);
$novel = get_post($novel_id);

if (!$novel) {
    wp_redirect(home_url('/404'));
    exit;
}

// الحصول على معلومات الفصل الحالي
$chapter_number = get_post_meta($post->ID, '_chapter_number', true);
$volume_number = get_post_meta($post->ID, '_volume_number', true);
$current_unique_id = get_post_meta($post->ID, '_chapter_unique_id', true);

// معالجة المعرف الفريد للفصل - إنشاء معرف جديد إذا لم يكن موجودًا
if (empty($current_unique_id)) {
    $current_unique_id = sekaiplu_get_chapter_unique_id($post->ID);
}

// الحصول على معرف الفصل الفريد من الرابط و/أو المترجم المطلوب
$requested_chapter_id = get_query_var('r', '');
$requested_author_id = get_query_var('author', '');

// الحصول على معرف الفصل الفريد
$chapter_unique_id = get_post_meta($post->ID, '_chapter_unique_id', true);

// الحصول على معرف المترجم المطلوب من الرابط (إذا وجد)
$requested_author_id = '';
if (!empty($_GET['author']) && is_numeric($_GET['author'])) {
    $requested_author_id = intval($_GET['author']);
}

// الحصول على معرف المترجم من المنشور الحالي
$current_author_id = get_post_field('post_author', $post->ID);

// إنشاء معرف الترجمة الفريد المطلوب
$requested_translation_id = !empty($requested_author_id) ? $chapter_unique_id . '_' . $requested_author_id : '';

// إنشاء معرف الترجمة الفريد الحالي
$current_translation_id = $chapter_unique_id . '_' . $current_author_id;

// للتصحيح فقط
if (current_user_can('manage_options')) {
    error_log("SEKAIPLU DEBUG: المعرف الفريد للفصل الحالي: {$chapter_unique_id}");
    error_log("SEKAIPLU DEBUG: المعرف الفريد للترجمة الحالية: {$current_translation_id}");
    error_log("SEKAIPLU DEBUG: المعرف الفريد للترجمة المطلوبة: {$requested_translation_id}");
}

// تحديد ما إذا كنا بحاجة للبحث عن ترجمة مختلفة
$need_different_translation = false;

// إذا كان هناك معرف ترجمة مطلوب ومختلف عن الحالي
if (!empty($requested_translation_id) && $requested_translation_id !== $current_translation_id) {
    $need_different_translation = true;
}

// إذا كان هناك معرف منشور مطلوب مباشرة من خلال معلمة 'r'
// وهذا المعرف مختلف عن المنشور الحالي
if (!empty($_GET['r']) && is_numeric($_GET['r']) && intval($_GET['r']) !== $post->ID) {
    $need_different_translation = true;
}

// إذا كان هناك معرف مترجم مفضل في الكوكيز وهو مختلف عن المترجم الحالي
$preferred_translator = sekaiplu_get_preferred_translator();
if (!empty($preferred_translator)) {
    $user = get_user_by('slug', $preferred_translator);
    if ($user && $user->ID !== $current_author_id && empty($requested_author_id)) {
        $need_different_translation = true;
        $requested_author_id = $user->ID;
    }
}

// إذا كنا بحاجة لترجمة مختلفة، فلنبحث عنها
if ($need_different_translation) {
    // استدعاء دالة تحديد الترجمة المناسبة
    $current_translation = sekaiplu_determine_translation($chapter_unique_id, $requested_author_id);

    if ($current_translation) {
        // للتصحيح فقط
        if (current_user_can('manage_options')) {
            error_log("SEKAIPLU DEBUG: منشور مخصص للمترجم - المطلوب: {$requested_author_id} | المنشور: {$current_translation->post_author}");
        }

        // تحديث المتغيرات العامة لعرض الفصل المطلوب
        $GLOBALS['post'] = $current_translation;
        setup_postdata($current_translation);

        // تحديث المتغيرات المستخدمة في العرض
        $current_author_id = get_post_field('post_author', $current_translation->ID);
        $current_translator_nicename = get_the_author_meta('user_nicename', $current_author_id);

        // تحديث معرف الرواية ومعلوماتها بعد تغيير الترجمة
        $novel_id = get_post_meta($current_translation->ID, '_novel_id', true);
        $novel = get_post($novel_id);

        // تحديث معلومات الفصل الحالي
        $chapter_number = get_post_meta($current_translation->ID, '_chapter_number', true);
        $volume_number = get_post_meta($current_translation->ID, '_volume_number', true);

        // عرض معلومات التصحيح للمسؤولين
        if (current_user_can('manage_options')) {
            echo "<div class='alert alert-success mb-3'>";
            echo "<strong>معلومات الترجمة:</strong><br>";
            echo "معرف الفصل: " . $current_translation->ID . "<br>";
            echo "معرف الكاتب: " . $current_author_id . "<br>";
            echo "اسم المترجم: " . $current_translator_nicename . "<br>";
            echo "معرف الرواية: " . $novel_id . "<br>";
            echo "عنوان الرواية: " . $novel->post_title . "<br>";
            echo "سبب الاختيار: " . sekaiplu_get_translation_selection_reason($current_translation->ID, $requested_author_id) . "<br>";
            echo "</div>";
        }
    } else {
        echo '<div class="alert alert-warning">عذراً، لم يتم العثور على هذا الفصل.</div>';
        // عودة إلى الفصل الحالي
        setup_postdata($post);
    }
} else {
    // تحديث المتغيرات المستخدمة في العرض للفصل الحالي
    $current_author_id = get_post_field('post_author', $post->ID);
    $current_translator_nicename = get_the_author_meta('user_nicename', $current_author_id);
}

// استخدام المترجم من الرابط أو من الكوكيز للبحث عن الفصول المجاورة
if (empty($requested_author_id) && isset($_COOKIE['preferred_translator'])) {
    $current_translator_nicename = sanitize_text_field($_COOKIE['preferred_translator']);
}

// للتصحيح فقط
if (current_user_can('manage_options')) {
    error_log("SEKAIPLU DEBUG: معرف الرواية المستخدم للبحث عن الفصول المجاورة: {$novel_id}");
    error_log("SEKAIPLU DEBUG: اسم المترجم المستخدم للبحث عن الفصول المجاورة: {$current_translator_nicename}");
    error_log("SEKAIPLU DEBUG: عنوان الرواية المستخدم: {$novel->post_title}");
}

// الحصول على الفصل السابق والتالي (من نفس المترجم)
$adjacent_chapters = get_posts(array(
    'post_type' => 'chapter',
    'posts_per_page' => -1,
    'author_name' => $current_translator_nicename,
    'meta_query' => array(
        array(
            'key' => '_novel_id',
            'value' => $novel_id
        ),
        array(
            'key' => '_volume_number',
            'value' => $volume_number
        )
    ),
    'orderby' => 'meta_value_num',
    'meta_key' => '_chapter_number',
    'order' => 'ASC'
));

$current_index = 0;
$current_post_id = $post->ID; // استخدام معرف المنشور الحالي بعد تحديثه

foreach ($adjacent_chapters as $index => $ch) {
    if ($ch->ID === $current_post_id) {
        $current_index = $index;
        break;
    }
}

$prev_chapter = $current_index > 0 ? $adjacent_chapters[$current_index - 1] : null;
$next_chapter = $current_index < count($adjacent_chapters) - 1 ? $adjacent_chapters[$current_index + 1] : null;

// الحصول على جميع ترجمات هذا الفصل
$other_translations = sekaiplu_get_chapter_translations($post->ID);

// الحصول على معلومات الإعجابات
$likes_count = intval(get_post_meta($post->ID, '_likes_count', true));
$likes_users = get_post_meta($post->ID, '_likes_users', true);
$has_liked = is_user_logged_in() && is_array($likes_users) && in_array(get_current_user_id(), $likes_users);

// الحصول على عدد المشاهدات
$views_count = intval(get_post_meta($post->ID, '_views_count', true));
?>

<!-- شريط التنقل العلوي الثابت -->
<div class="chapter-navbar">
    <div class="container">
        <div class="chapter-navbar-content">
            <div class="chapter-nav-left">
                <?php if ($prev_chapter): ?>
                    <a href="<?php echo get_permalink($prev_chapter->ID); ?>" class="chapter-nav-btn prev-btn" title="الفصل السابق">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                <?php else: ?>
                    <span class="chapter-nav-btn disabled">
                        <i class="fas fa-chevron-right"></i>
                    </span>
                <?php endif; ?>
            </div>

            <div class="chapter-nav-center">
                <h1 class="chapter-nav-title">
                    <span class="novel-title"><?php echo esc_html($novel->post_title); ?></span>
                    <span class="chapter-number">الفصل <?php echo esc_html($chapter_number); ?></span>
                </h1>
            </div>

            <div class="chapter-nav-right">
                <?php if ($next_chapter): ?>
                    <a href="<?php echo get_permalink($next_chapter->ID); ?>" class="chapter-nav-btn next-btn" title="الفصل التالي">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                <?php else: ?>
                    <span class="chapter-nav-btn disabled">
                        <i class="fas fa-chevron-left"></i>
                    </span>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<div class="container py-4">
    <div class="chapter-container">
        <!-- معلومات الفصل والتنقل -->
        <div class="chapter-header-card">
            <div class="chapter-breadcrumb">
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item">
                            <a href="<?php echo get_permalink($novel->ID); ?>">
                                <?php echo esc_html($novel->post_title); ?>
                            </a>
                        </li>
                        <li class="breadcrumb-item">
                            <a href="<?php echo home_url('/novel/?r=' . get_post_meta($novel->ID, '_novel_unique_id', true) . '/' . $volume_number); ?>">
                                المجلد <?php echo esc_html($volume_number); ?>
                            </a>
                        </li>
                        <li class="breadcrumb-item active">
                            الفصل <?php echo esc_html($chapter_number); ?>
                        </li>
                    </ol>
                </nav>
            </div>

            <div class="chapter-title-section">
                <h1 class="chapter-title">
                    <?php echo esc_html($novel->post_title); ?> -
                    الفصل <?php echo esc_html($chapter_number); ?>
                    <?php if (get_the_title()): ?>
                        <span class="chapter-subtitle">- <?php the_title(); ?></span>
                    <?php endif; ?>
                </h1>
            </div>

            <div class="chapter-meta-section">
                <div class="chapter-meta-container">
                    <div class="translator-info">
                        <?php echo get_avatar($current_author_id, 40, '', '', array('class' => 'translator-avatar')); ?>
                        <div class="translator-details">
                            <a href="<?php echo esc_url(get_author_posts_url($current_author_id)); ?>"
                               class="translator-name">
                                <?php echo esc_html(get_the_author_meta('display_name', $current_author_id)); ?>
                            </a>
                            <span class="chapter-date">
                                <i class="far fa-calendar-alt"></i>
                                <?php echo get_the_date('Y-m-d'); ?>
                            </span>
                        </div>
                    </div>

                    <div class="chapter-stats">
                        <div class="stats-item views">
                            <i class="fas fa-eye"></i>
                            <span class="stats-count"><?php echo number_format(intval($views_count)); ?></span>
                        </div>

                        <div class="stats-item likes <?php echo $has_liked ? 'liked' : ''; ?>"
                             data-chapter-id="<?php echo $post->ID; ?>">
                            <i class="fas fa-heart"></i>
                            <span class="stats-count"><?php echo number_format($likes_count); ?></span>
                        </div>

                        <?php if (!empty($other_translations)): ?>
                            <div class="stats-item translations">
                                <?php sekaiplu_display_translations_dropdown($other_translations); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="chapter-controls">
                <div class="controls-group">
                    <button type="button" class="control-btn font-decrease" onclick="decreaseFontSize()" title="تصغير الخط">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button type="button" class="control-btn font-default" onclick="resetFontSize()" title="حجم الخط الافتراضي">
                        <i class="fas fa-font"></i>
                    </button>
                    <button type="button" class="control-btn font-increase" onclick="increaseFontSize()" title="تكبير الخط">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>

                <div class="controls-group">
                    <button type="button" class="control-btn read-aloud" onclick="readChapter()" title="قراءة الفصل">
                        <i class="fas fa-volume-up"></i>
                    </button>
                    <button type="button" class="control-btn stop-reading" onclick="stopReading()" title="إيقاف القراءة">
                        <i class="fas fa-stop"></i>
                    </button>
                </div>
            </div>
        </div>

            <!-- محتوى الفصل -->
            <div class="chapter-content-card">
                <div class="chapter-content">
                    <?php
                    // عرض محتوى الفصل
                    if (!empty($requested_chapter_id) && $current_translation) {
                        // عرض محتوى الترجمة المحددة فقط
                        echo apply_filters('the_content', $current_translation->post_content);
                        sekaiplus_increment_chapter_views($current_translation->ID);
                    } else {
                        // عرض المحتوى الافتراضي للفصل الحالي
                        the_content();
                        sekaiplus_increment_chapter_views($post->ID);
                    }
                    ?>
                </div>

                <div class="chapter-end-mark">
                    <span class="end-mark-line"></span>
                    <span class="end-mark-text">نهاية الفصل</span>
                    <span class="end-mark-line"></span>
                </div>
            </div>

            <?php
            // عرض قائمة بجميع الترجمات المتاحة بشكل منفصل عن المحتوى
            if (!empty($requested_chapter_id) && $current_translation) {
                $all_translations = get_posts(array(
                    'post_type' => 'chapter',
                    'posts_per_page' => -1,
                    'meta_query' => array(
                        array(
                            'key' => '_chapter_unique_id',
                            'value' => $requested_chapter_id
                        )
                    )
                ));

                if (count($all_translations) > 1) {
                    // احصل على الترجمات الأخرى (استبعاد الترجمة الحالية)
                    $other_translations = array_filter($all_translations, function($chapter) use ($current_translation) {
                        return $chapter->ID !== $current_translation->ID;
                    });

                    // تحويل مصفوفة الترجمات إلى التنسيق المطلوب
                    $other_translations_formatted = array_map(function($chapter) {
                        $translator_id = $chapter->post_author;
                        return array(
                            'ID' => $chapter->ID,
                            'unique_id' => get_post_meta($chapter->ID, '_chapter_unique_id', true),
                            'translator_id' => $translator_id,
                            'translator_nicename' => get_the_author_meta('user_nicename', $translator_id),
                            'translator_name' => get_the_author_meta('display_name', $translator_id),
                            'translator_avatar' => get_avatar_url($translator_id, array('size' => 32)),
                            'date' => get_the_date('Y-m-d', $chapter->ID),
                            'status' => get_post_status($chapter->ID),
                            'likes_count' => intval(get_post_meta($chapter->ID, '_likes_count', true)),
                            'views_count' => intval(get_post_meta($chapter->ID, '_views_count', true))
                        );
                    }, $other_translations);

                    // عرض قائمة الترجمات في بطاقة منفصلة
                    echo '<div class="other-translations-card">';
                    echo '<h3 class="other-translations-title">ترجمات أخرى متاحة</h3>';
                    echo '<div class="other-translations-list">';
                    sekaiplu_display_translations_list($other_translations_formatted, $current_translation->ID, $current_translator_nicename);
                    echo '</div></div>';
                }
            }
            ?>

            <!-- أزرار التنقل -->
            <div class="chapter-navigation">
                <div class="nav-buttons">
                    <?php if ($prev_chapter): ?>
                        <a href="<?php echo get_permalink($prev_chapter->ID); ?>" class="nav-btn prev-chapter">
                            <i class="fas fa-chevron-right"></i>
                            <span>الفصل السابق</span>
                        </a>
                    <?php else: ?>
                        <span class="nav-btn disabled">
                            <i class="fas fa-chevron-right"></i>
                            <span>لا يوجد فصل سابق</span>
                        </span>
                    <?php endif; ?>

                    <a href="<?php echo get_permalink($novel->ID); ?>" class="nav-btn to-index">
                        <i class="fas fa-list"></i>
                        <span>فهرس الفصول</span>
                    </a>

                    <?php if ($next_chapter): ?>
                        <a href="<?php echo get_permalink($next_chapter->ID); ?>" class="nav-btn next-chapter">
                            <span>الفصل التالي</span>
                            <i class="fas fa-chevron-left"></i>
                        </a>
                    <?php else: ?>
                        <span class="nav-btn disabled">
                            <span>لا يوجد فصل تالي</span>
                            <i class="fas fa-chevron-left"></i>
                        </span>
                    <?php endif; ?>
                </div>

                <div class="novel-return">
                    <a href="<?php echo get_permalink($novel->ID); ?>" class="return-btn">
                        <i class="fas fa-book"></i>
                        <span>العودة للرواية</span>
                    </a>
                </div>
            </div>

            <!-- التعليقات -->
            <div class="comments-card">
                    <?php comments_template(); ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
:root {
    --chapter-primary: var(--bs-primary, #0d6efd);
    --chapter-secondary: var(--bs-secondary, #6c757d);
    --chapter-success: var(--bs-success, #198754);
    --chapter-danger: var(--bs-danger, #dc3545);
    --chapter-warning: var(--bs-warning, #ffc107);
    --chapter-info: var(--bs-info, #0dcaf0);
    --chapter-light: var(--bs-light, #f8f9fa);
    --chapter-dark: var(--bs-dark, #212529);
    --chapter-bg: var(--bs-body-bg, #fff);
    --chapter-text: var(--bs-body-color, #212529);
    --chapter-border: rgba(0, 0, 0, 0.125);
    --chapter-shadow: rgba(0, 0, 0, 0.1);
    --chapter-hover-bg: rgba(0, 0, 0, 0.03);
    --chapter-transition: all 0.3s ease;
}

.dark-mode {
    --chapter-bg: var(--bs-dark, #212529);
    --chapter-text: var(--bs-light, #f8f9fa);
    --chapter-border: rgba(255, 255, 255, 0.125);
    --chapter-shadow: rgba(0, 0, 0, 0.25);
    --chapter-hover-bg: rgba(255, 255, 255, 0.05);
}

/* ===== شريط التنقل العلوي ===== */
.chapter-navbar {
    position: sticky;
    top: 0;
    background-color: var(--chapter-bg);
    box-shadow: 0 2px 10px var(--chapter-shadow);
    padding: 0.75rem 0;
    z-index: 1000;
    transition: var(--chapter-transition);
}

.chapter-navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.chapter-nav-left,
.chapter-nav-right {
    flex: 0 0 auto;
}

.chapter-nav-center {
    flex: 1;
    text-align: center;
    padding: 0 1rem;
}

.chapter-nav-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.novel-title {
    font-size: 0.9rem;
    color: var(--chapter-secondary);
    margin-bottom: 0.25rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.chapter-number {
    color: var(--chapter-primary);
}

.chapter-nav-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background-color: var(--chapter-bg);
    color: var(--chapter-primary);
    border: 1px solid var(--chapter-primary);
    transition: var(--chapter-transition);
    text-decoration: none;
}

.chapter-nav-btn:hover {
    background-color: var(--chapter-primary);
    color: white;
    transform: scale(1.05);
}

.chapter-nav-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    border-color: var(--chapter-secondary);
    color: var(--chapter-secondary);
}

/* ===== هيكل الصفحة ===== */
.chapter-container {
    max-width: 900px;
    margin: 0 auto;
}

/* ===== بطاقة معلومات الفصل ===== */
.chapter-header-card {
    background-color: var(--chapter-bg);
    border-radius: 0.5rem;
    box-shadow: 0 0.25rem 1rem var(--chapter-shadow);
    padding: 1.5rem;
    margin-bottom: 2rem;
    transition: var(--chapter-transition);
}

.chapter-breadcrumb {
    margin-bottom: 1.25rem;
}

.breadcrumb {
    margin-bottom: 0;
}

.breadcrumb-item a {
    color: var(--chapter-primary);
    text-decoration: none;
    transition: var(--chapter-transition);
}

.breadcrumb-item a:hover {
    color: rgba(var(--bs-primary-rgb), 0.8);
}

.breadcrumb-item.active {
    color: var(--chapter-secondary);
}

.chapter-title-section {
    margin-bottom: 1.5rem;
    border-bottom: 1px solid var(--chapter-border);
    padding-bottom: 1.25rem;
}

.chapter-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0;
    color: var(--chapter-text);
}

.chapter-subtitle {
    font-weight: 500;
    color: var(--chapter-secondary);
}

.chapter-meta-section {
    margin-bottom: 1.5rem;
}

.chapter-meta-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
    background-color: rgba(var(--bs-primary-rgb), 0.05);
    border-radius: 0.75rem;
    padding: 1rem;
}

.translator-info {
    display: -webkit-box;
    align-items: center;
}

.translator-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    margin-right: 0.75rem;
    object-fit: cover;
    border: 2px solid var(--chapter-primary);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.1);
}

.translator-details {
    display: flex;
    flex-direction: column;
}

.translator-name {
    font-weight: 600;
    color: var(--chapter-text);
    text-decoration: none;
    transition: var(--chapter-transition);
}

.translator-name:hover {
    color: var(--chapter-primary);
}

.chapter-date {
    font-size: 0.875rem;
    color: var(--chapter-secondary);
    display: flow;
    align-items: center;
}

.chapter-date i {
    margin-right: 0.5rem;
}

.chapter-stats {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.stats-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    border-radius: 2rem;
    background-color: var(--chapter-bg);
    border: 1px solid rgba(var(--bs-primary-rgb), 0.2);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: var(--chapter-transition);
}

.stats-item.views {
    color: var(--chapter-secondary);
}

.stats-item.likes {
    color: var(--chapter-secondary);
    cursor: pointer;
}

.stats-item.likes:hover {
    background-color: rgba(var(--bs-danger-rgb), 0.1);
    color: var(--chapter-danger);
}

.stats-item.likes.liked {
    background-color: rgba(var(--bs-danger-rgb), 0.1);
    color: var(--chapter-danger);
}

.chapter-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
    border-top: 1px solid var(--chapter-border);
    padding-top: 1.25rem;
}

.controls-group {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.control-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 2.5rem;
    height: 2.5rem;
    border-radius: 50%;
    background-color: var(--chapter-bg);
    color: var(--chapter-primary);
    border: 1px solid var(--chapter-primary);
    transition: var(--chapter-transition);
    cursor: pointer;
}

.control-btn:hover {
    background-color: var(--chapter-primary);
    color: white;
    transform: scale(1.05);
}

.control-btn.read-aloud {
    background-color: rgba(var(--bs-success-rgb), 0.1);
    color: var(--chapter-success);
    border-color: var(--chapter-success);
}

.control-btn.read-aloud:hover {
    background-color: var(--chapter-success);
    color: white;
}

.control-btn.stop-reading {
    background-color: rgba(var(--bs-danger-rgb), 0.1);
    color: var(--chapter-danger);
    border-color: var(--chapter-danger);
}

.control-btn.stop-reading:hover {
    background-color: var(--chapter-danger);
    color: white;
}

/* ===== محتوى الفصل ===== */
.chapter-content-card {
    background-color: var(--chapter-bg);
    border-radius: 0.5rem;
    box-shadow: 0 0.25rem 1rem var(--chapter-shadow);
    padding: 2rem;
    margin-bottom: 2rem;
    transition: var(--chapter-transition);
}

.chapter-content {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--chapter-text);
}

.chapter-content p {
    margin-bottom: 1.5rem;
}

.chapter-content img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    margin: 1.5rem 0;
}

.chapter-end-mark {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-top: 3rem;
    color: var(--chapter-secondary);
}

.end-mark-line {
    flex: 1;
    height: 1px;
    background-color: var(--chapter-border);
}

.end-mark-text {
    padding: 0 1rem;
    font-style: italic;
    font-weight: 500;
}

/* ===== ترجمات أخرى ===== */
.other-translations-card {
    background-color: var(--chapter-bg);
    border-radius: 0.5rem;
    box-shadow: 0 0.25rem 1rem var(--chapter-shadow);
    padding: 1.5rem;
    margin-bottom: 2rem;
    transition: var(--chapter-transition);
}

.other-translations-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.25rem;
    color: var(--chapter-text);
    display: flex;
    align-items: center;
}

.other-translations-title::before {
    content: '\f1e0';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-right: 0.75rem;
    color: var(--chapter-primary);
}

/* ===== أزرار التنقل ===== */
.chapter-navigation {
    background-color: var(--chapter-bg);
    border-radius: 0.5rem;
    box-shadow: 0 0.25rem 1rem var(--chapter-shadow);
    padding: 1.5rem;
    margin-bottom: 2rem;
    transition: var(--chapter-transition);
}

.nav-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
    margin-bottom: 1.25rem;
}

.nav-btn {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.25rem;
    border-radius: 2rem;
    background-color: var(--chapter-primary);
    color: white;
    text-decoration: none;
    transition: var(--chapter-transition);
    font-weight: 500;
}

.nav-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1rem rgba(var(--bs-primary-rgb), 0.2);
    color: white;
}

.nav-btn.prev-chapter i {
    margin-right: 0.75rem;
}

.nav-btn.next-chapter i {
    margin-left: 0.75rem;
}

.nav-btn.to-index {
    background-color: var(--chapter-bg);
    color: var(--chapter-primary);
    border: 1px solid var(--chapter-primary);
}

.nav-btn.to-index:hover {
    background-color: var(--chapter-primary);
    color: white;
}

.nav-btn.to-index i {
    margin-right: 0.75rem;
}

.nav-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: var(--chapter-secondary);
}

.nav-btn.disabled:hover {
    transform: none;
    box-shadow: none;
}

.novel-return {
    display: flex;
    justify-content: center;
    border-top: 1px solid var(--chapter-border);
    padding-top: 1.25rem;
}

.return-btn {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    border-radius: 2rem;
    background-color: var(--chapter-bg);
    color: var(--chapter-secondary);
    border: 1px solid var(--chapter-secondary);
    text-decoration: none;
    transition: var(--chapter-transition);
    font-weight: 500;
}

.return-btn:hover {
    background-color: var(--chapter-secondary);
    color: white;
}

.return-btn i {
    margin-right: 0.75rem;
}

/* ===== التعليقات ===== */
.comments-card {
    background-color: var(--chapter-bg);
    border-radius: 0.5rem;
    box-shadow: 0 0.25rem 1rem var(--chapter-shadow);
    padding: 1.5rem;
    margin-bottom: 2rem;
    transition: var(--chapter-transition);
}

.comments-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 1.25rem;
    color: var(--chapter-text);
    display: flex;
    align-items: center;
}

.comments-title::before {
    content: '\f086';
    font-family: 'Font Awesome 5 Free';
    font-weight: 900;
    margin-right: 0.75rem;
    color: var(--chapter-primary);
}

/* ===== تصميم متجاوب ===== */
@media (max-width: 991.98px) {
    .chapter-container {
        padding: 0 1rem;
    }

    .chapter-meta-container {
        flex-direction: column;
        align-items: center;
    }

    .translator-info {
        margin-bottom: 0.75rem;
    }

    .chapter-stats {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 767.98px) {
    .chapter-nav-title {
        font-size: 1rem;
    }

    .novel-title {
        font-size: 0.8rem;
    }

    .chapter-title {
        font-size: 1.25rem;
    }

    .chapter-content-card {
        padding: 1.5rem;
    }

    .chapter-content {
        font-size: 1rem;
        line-height: 1.7;
    }

    .nav-buttons {
        flex-direction: column;
        width: 100%;
    }

    .nav-btn {
        width: 100%;
        justify-content: center;
    }
}

@media (max-width: 575.98px) {
    .chapter-header-card,
    .chapter-content-card,
    .other-translations-card,
    .chapter-navigation,
    .comments-card {
        padding: 1rem;
        border-radius: 0.375rem;
    }

    .chapter-controls {
        flex-direction: column;
        align-items: flex-start;
    }

    .controls-group {
        width: 100%;
        justify-content: space-between;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // تغيير حجم الخط
    window.currentFontSize = 1.1;

    window.increaseFontSize = function() {
        if (window.currentFontSize < 1.5) {
            window.currentFontSize += 0.1;
            document.querySelector('.chapter-content').style.fontSize = window.currentFontSize + 'rem';
            localStorage.setItem('chapterFontSize', window.currentFontSize);
        }
    };

    window.decreaseFontSize = function() {
        if (window.currentFontSize > 0.9) {
            window.currentFontSize -= 0.1;
            document.querySelector('.chapter-content').style.fontSize = window.currentFontSize + 'rem';
            localStorage.setItem('chapterFontSize', window.currentFontSize);
        }
    };

    window.resetFontSize = function() {
        window.currentFontSize = 1.1;
        document.querySelector('.chapter-content').style.fontSize = window.currentFontSize + 'rem';
        localStorage.setItem('chapterFontSize', window.currentFontSize);
    };

    // استرجاع حجم الخط المحفوظ
    const savedFontSize = localStorage.getItem('chapterFontSize');
    if (savedFontSize) {
        window.currentFontSize = parseFloat(savedFontSize);
        document.querySelector('.chapter-content').style.fontSize = window.currentFontSize + 'rem';
    }

    // إضافة الإعجاب
    const likeButton = document.querySelector('.stats-item.likes');
    if (likeButton) {
        likeButton.addEventListener('click', function() {
            const chapterId = this.getAttribute('data-chapter-id');

            fetch(sekaiplus_ajax.ajax_url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                body: new URLSearchParams({
                    action: 'like_chapter',
                    chapter_id: chapterId,
                    nonce: sekaiplus_ajax.nonce
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const countElement = this.querySelector('.stats-count');
                    countElement.textContent = data.data.count;

                    if (data.data.liked) {
                        this.classList.add('liked');
                    } else {
                        this.classList.remove('liked');
                    }
                }
            })
            .catch(error => {
                console.error('Error:', error);
            });
        });
    }

    // تحديد موضع القراءة
    const saveReadingPosition = function() {
        const scrollPosition = window.scrollY;
        const contentElement = document.querySelector('.chapter-content');

        if (contentElement) {
            const contentTop = contentElement.getBoundingClientRect().top + window.scrollY;
            const relativePosition = scrollPosition - contentTop;

            if (relativePosition > 0) {
                const chapterId = document.querySelector('.stats-item.likes').getAttribute('data-chapter-id');
                localStorage.setItem('readingPosition_' + chapterId, relativePosition);
            }
        }
    };

    // حفظ موضع القراءة عند التمرير
    window.addEventListener('scroll', function() {
        clearTimeout(window.scrollTimer);
        window.scrollTimer = setTimeout(saveReadingPosition, 200);
    });

    // استرجاع موضع القراءة
    const restoreReadingPosition = function() {
        const contentElement = document.querySelector('.chapter-content');
        const likeButton = document.querySelector('.stats-item.likes');

        if (contentElement && likeButton) {
            const chapterId = likeButton.getAttribute('data-chapter-id');
            const savedPosition = localStorage.getItem('readingPosition_' + chapterId);

            if (savedPosition) {
                const contentTop = contentElement.getBoundingClientRect().top + window.scrollY;
                const scrollToPosition = contentTop + parseFloat(savedPosition);

                setTimeout(function() {
                    window.scrollTo({
                        top: scrollToPosition,
                        behavior: 'smooth'
                    });
                }, 300);
            }
        }
    };

    // استرجاع موضع القراءة عند تحميل الصفحة
    restoreReadingPosition();
});

// قراءة الفصل بصوت عالٍ
function readChapter() {
    const content = document.querySelector('.chapter-content');
    if (!content) return alert("تعذّر العثور على محتوى الفصل.");

    const text = content.innerText;
    const utterance = new SpeechSynthesisUtterance(text);
    utterance.lang = 'ar-SA'; // اللغة العربية
    utterance.rate = 1;       // سرعة القراءة
    utterance.pitch = 1;      // نغمة الصوت

    window.speechSynthesis.cancel(); // إيقاف أي قراءة سابقة
    window.speechSynthesis.speak(utterance);

    // تغيير حالة زر القراءة
    const readButton = document.querySelector('.control-btn.read-aloud');
    if (readButton) {
        readButton.style.display = 'none';
    }

    const stopButton = document.querySelector('.control-btn.stop-reading');
    if (stopButton) {
        stopButton.style.display = 'inline-flex';
    }
}

// إيقاف القراءة
function stopReading() {
    window.speechSynthesis.cancel();

    // تغيير حالة زر القراءة
    const readButton = document.querySelector('.control-btn.read-aloud');
    if (readButton) {
        readButton.style.display = 'inline-flex';
    }

    const stopButton = document.querySelector('.control-btn.stop-reading');
    if (stopButton) {
        stopButton.style.display = 'none';
    }
}

// إخفاء زر إيقاف القراءة في البداية
document.addEventListener('DOMContentLoaded', function() {
    const stopButton = document.querySelector('.control-btn.stop-reading');
    if (stopButton) {
        stopButton.style.display = 'none';
    }
});
</script>

<?php
get_footer();

