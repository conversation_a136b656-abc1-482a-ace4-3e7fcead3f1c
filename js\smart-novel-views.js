/**
 * نظام ذكي لتتبع مشاهدات الروايات
 * يوفر تحديثات فورية ومؤشرات بصرية للمشاهدات
 */

(function($) {
    'use strict';

    // متغيرات عامة
    let viewUpdateTimer = null;
    let hasViewBeenCounted = false;
    let pageLoadTime = null;
    let isPageVisible = true;
    let scrollDepth = 0;

    // إعدادات النظام
    const settings = {
        minViewTime: 15000,     // 15 ثانية كحد أدنى للمشاهدة
        updateInterval: 45000,  // تحديث كل 45 ثانية
        scrollThreshold: 0.2,   // 20% من الصفحة يجب تصفحها
        maxRetries: 3
    };

    /**
     * تهيئة النظام عند تحميل الصفحة
     */
    $(document).ready(function() {
        initializeNovelViewTracking();
        setupVisibilityTracking();
        setupScrollTracking();
        setupTimeTracking();
    });

    /**
     * تهيئة تتبع مشاهدات الروايات
     */
    function initializeNovelViewTracking() {
        const novelElement = $('.novel-views-count');
        if (!novelElement.length) return;

        const novelId = novelElement.data('novel-id');
        if (!novelId) return;

        pageLoadTime = Date.now();
        
        // تأخير تسجيل المشاهدة للتأكد من المشاهدة الفعلية
        setTimeout(() => {
            if (isValidNovelView()) {
                recordNovelView(novelId);
            }
        }, settings.minViewTime);

        // تحديث دوري للإحصائيات
        setInterval(() => {
            updateNovelViewStats(novelId);
        }, settings.updateInterval);
    }

    /**
     * تتبع رؤية الصفحة
     */
    function setupVisibilityTracking() {
        if (typeof document.hidden !== "undefined") {
            document.addEventListener("visibilitychange", function() {
                isPageVisible = !document.hidden;
                
                if (isPageVisible && pageLoadTime) {
                    // إعادة تعيين وقت البداية عند العودة للصفحة
                    pageLoadTime = Date.now();
                }
            });
        }
    }

    /**
     * تتبع التمرير لقياس مستوى التفاعل
     */
    function setupScrollTracking() {
        let maxScrollPercentage = 0;
        
        $(window).on('scroll', function() {
            const scrollTop = $(window).scrollTop();
            const documentHeight = $(document).height();
            const windowHeight = $(window).height();
            const scrollPercentage = scrollTop / (documentHeight - windowHeight);
            
            maxScrollPercentage = Math.max(maxScrollPercentage, scrollPercentage);
            scrollDepth = maxScrollPercentage;
            
            // حفظ عمق التمرير في localStorage
            const novelElement = $('.novel-views-count');
            if (novelElement.length) {
                const novelId = novelElement.data('novel-id');
                if (novelId) {
                    localStorage.setItem(`novel_scroll_depth_${novelId}`, scrollDepth);
                }
            }
        });
    }

    /**
     * تتبع الوقت المقضي
     */
    function setupTimeTracking() {
        // حفظ الوقت المقضي عند مغادرة الصفحة
        window.addEventListener('beforeunload', function() {
            if (pageLoadTime) {
                const timeSpent = Date.now() - pageLoadTime;
                const novelElement = $('.novel-views-count');
                if (novelElement.length) {
                    const novelId = novelElement.data('novel-id');
                    if (novelId) {
                        localStorage.setItem(`novel_time_spent_${novelId}`, timeSpent);
                    }
                }
            }
        });
    }

    /**
     * التحقق من صحة مشاهدة الرواية
     */
    function isValidNovelView() {
        if (!isPageVisible) return false;
        if (!pageLoadTime) return false;
        
        const timeSpent = Date.now() - pageLoadTime;
        if (timeSpent < settings.minViewTime) return false;

        // التحقق من عمق التمرير
        if (scrollDepth < settings.scrollThreshold) return false;

        return true;
    }

    /**
     * تسجيل مشاهدة الرواية
     */
    function recordNovelView(novelId, retryCount = 0) {
        if (hasViewBeenCounted) return;

        const timeSpent = Date.now() - pageLoadTime;

        $.ajax({
            url: sekaiplus_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'record_smart_novel_view',
                novel_id: novelId,
                time_spent: timeSpent,
                scroll_depth: scrollDepth,
                nonce: sekaiplus_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    hasViewBeenCounted = true;
                    updateNovelViewDisplay(response.data);
                    showNovelViewConfirmation();
                } else {
                    console.warn('Failed to record novel view:', response.data);
                }
            },
            error: function(xhr, status, error) {
                console.error('Novel view recording error:', error);
                
                // إعادة المحاولة
                if (retryCount < settings.maxRetries) {
                    setTimeout(() => {
                        recordNovelView(novelId, retryCount + 1);
                    }, 3000 * (retryCount + 1));
                }
            }
        });
    }

    /**
     * تحديث عرض مشاهدات الرواية
     */
    function updateNovelViewDisplay(data) {
        const viewsElement = $('.novel-views-count');
        if (viewsElement.length && data.new_count) {
            // تأثير بصري للتحديث
            viewsElement.addClass('updating');
            
            setTimeout(() => {
                viewsElement.text(formatNumber(data.new_count));
                viewsElement.removeClass('updating');
            }, 300);
        }

        // تحديث مؤشر المشاهدات اليومية
        if (data.today_views) {
            updateTodayNovelViewsIndicator(data.today_views);
        }
    }

    /**
     * تحديث إحصائيات مشاهدات الرواية
     */
    function updateNovelViewStats(novelId) {
        $.ajax({
            url: sekaiplus_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'get_novel_view_stats',
                novel_id: novelId,
                nonce: sekaiplus_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    updateNovelStatsDisplay(response.data);
                }
            },
            error: function(xhr, status, error) {
                console.error('Novel stats update error:', error);
            }
        });
    }

    /**
     * تحديث عرض إحصائيات الرواية
     */
    function updateNovelStatsDisplay(stats) {
        // تحديث العدد الإجمالي
        const totalElement = $('.novel-views-count');
        if (totalElement.length) {
            totalElement.text(formatNumber(stats.total_views));
        }

        // تحديث مؤشر اليوم
        updateTodayNovelViewsIndicator(stats.today_views);

        // تحديث إحصائيات المسؤولين
        updateAdminNovelStats(stats);
    }

    /**
     * تحديث مؤشر مشاهدات اليوم للرواية
     */
    function updateTodayNovelViewsIndicator(todayViews) {
        let indicator = $('.today-novel-views-indicator');
        
        if (todayViews > 0) {
            if (!indicator.length) {
                indicator = $('<span class="today-novel-views-indicator"></span>');
                $('.novel-views-count').parent().append(indicator);
            }
            
            indicator.text(`+${todayViews}`)
                    .attr('title', `مشاهدات اليوم: ${todayViews}`)
                    .addClass('pulse-animation');
            
            setTimeout(() => {
                indicator.removeClass('pulse-animation');
            }, 1000);
        } else if (indicator.length) {
            indicator.fadeOut(300, function() {
                $(this).remove();
            });
        }
    }

    /**
     * تحديث إحصائيات المسؤولين للرواية
     */
    function updateAdminNovelStats(stats) {
        const adminStats = $('.admin-novel-view-stats');
        if (!adminStats.length) return;

        let statsHtml = '<strong style="color: #0073aa; font-size: 14px; display: block; margin-bottom: 8px; border-bottom: 1px solid rgba(0, 115, 170, 0.2); padding-bottom: 5px;">إحصائيات مشاهدات الرواية (للمسؤولين فقط):</strong>';
        statsHtml += `إجمالي المشاهدات: ${formatNumber(stats.total_views)}<br>`;
        statsHtml += `المشاهدات الفريدة: ${formatNumber(stats.unique_views)}<br>`;
        statsHtml += `مشاهدات اليوم: ${formatNumber(stats.today_views)}<br>`;
        statsHtml += `مشاهدات الأسبوع: ${formatNumber(stats.week_views)}<br>`;
        statsHtml += `مشاهدات الشهر: ${formatNumber(stats.month_views)}<br>`;
        
        if (stats.avg_time_spent) {
            const avgMinutes = Math.round(stats.avg_time_spent / 60 * 10) / 10;
            statsHtml += `متوسط الوقت المقضي: ${avgMinutes} دقيقة<br>`;
        }
        
        adminStats.html(statsHtml);
    }

    /**
     * عرض تأكيد تسجيل مشاهدة الرواية
     */
    function showNovelViewConfirmation() {
        // إضافة تأثير بصري خفيف لتأكيد التسجيل
        const viewsElement = $('.novel-views-count').parent();
        viewsElement.addClass('novel-view-recorded');
        
        setTimeout(() => {
            viewsElement.removeClass('novel-view-recorded');
        }, 2000);
    }

    /**
     * تنسيق الأرقام
     */
    function formatNumber(num) {
        return new Intl.NumberFormat('ar-SA').format(num);
    }

    // إضافة CSS للتأثيرات البصرية
    $('<style>')
        .prop('type', 'text/css')
        .html(`
            .novel-views-count.updating {
                animation: novelCountUpdate 0.6s ease-in-out;
            }
            
            @keyframes novelCountUpdate {
                0% { transform: scale(1); }
                50% { transform: scale(1.1); color: var(--bs-primary, #0d6efd); }
                100% { transform: scale(1); }
            }
            
            .today-novel-views-indicator {
                position: relative;
                display: inline-block;
                background: linear-gradient(45deg, #28a745, #20c997);
                color: white;
                font-size: 0.75rem;
                font-weight: 600;
                padding: 2px 8px;
                border-radius: 12px;
                margin-left: 8px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
                animation: novelIndicatorPulse 2s infinite;
            }
            
            .today-novel-views-indicator.pulse-animation {
                animation: novelIndicatorPulse 1s ease-in-out;
            }
            
            @keyframes novelIndicatorPulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }
            
            .novel-view-recorded {
                box-shadow: 0 0 15px rgba(40, 167, 69, 0.3);
                transition: box-shadow 0.3s ease;
            }
            
            .admin-novel-view-stats {
                background: linear-gradient(135deg, #f0f8ff 0%, #e6f3ff 100%);
                border: 1px solid #0073aa;
                border-radius: 8px;
                padding: 15px;
                margin: 15px 0;
                font-size: 13px;
                box-shadow: 0 2px 8px rgba(0, 115, 170, 0.1);
            }
            
            .dark-mode .admin-novel-view-stats {
                background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
                border-color: #4a9eff;
                color: #e0e6ed;
            }
        `)
        .appendTo('head');

})(jQuery);
